2025-07-06 18:38:20,643 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-06 18:38:20,644 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-06 18:38:20,644 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-06 18:38:20,645 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-06 18:38:20,645 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-06 18:38:20,645 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-06 18:38:20,646 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-06 18:38:20,646 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-06 18:38:20,647 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-06 18:38:20,647 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-06 18:38:20,647 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-06 18:38:20,648 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-06 18:38:20,648 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-06 18:38:22,708 - __main__ - INFO - Existing processes terminated
2025-07-06 18:38:24,198 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-07-06 18:38:24,199 - utils.global_values_db - INFO - Global values database initialized successfully
2025-07-06 18:38:24,199 - utils.global_values_db - INFO - Using global values from config.py
2025-07-06 18:38:24,199 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-06 18:38:24,201 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-06 18:38:24,201 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-06 18:38:24,252 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-06 18:38:24,773 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-06 18:38:24,774 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-06 18:38:24,774 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-06 18:38:24,775 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-06 18:38:24,775 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-06 18:38:24,775 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-06 18:38:24,776 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-06 18:38:24,776 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-06 18:38:24,776 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-06 18:38:24,776 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-06 18:38:24,776 - utils.database - INFO - Database initialized successfully
2025-07-06 18:38:24,776 - utils.database - INFO - Checking initial database state...
2025-07-06 18:38:24,778 - utils.database - INFO - Database state: 0 suites, 0 cases, 10569 steps, 1 screenshots, 0 tracking entries
2025-07-06 18:38:24,797 - app - INFO - Using directories from config.py:
2025-07-06 18:38:24,797 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-06 18:38:24,797 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-06 18:38:24,797 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-06 18:38:24,927] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-06 18:38:24,939] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x116466a50>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-06 18:38:24,939] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-07-06 18:38:24,971] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-07-06 18:38:25,004] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-07-06 18:38:27,008] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-07-06 18:38:27,008] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-07-06 18:38:27,999] INFO in appium_device_controller: Installed Appium drivers: 
[2025-07-06 18:38:27,999] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-07-06 18:38:28,779] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-07-06 18:38:28,779] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-07-06 18:38:28,779] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-07-06 18:38:28,789] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-07-06 18:38:30,792] WARNING in appium_device_controller: Waiting for Appium server to start (attempt 1/15): HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1165756d0>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-06 18:38:32,809] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:32,809] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:34,817] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:34,818] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:36,825] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:36,825] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:38,836] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:38,836] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:40,845] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:40,845] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:42,852] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:42,853] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:44,863] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:44,863] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:46,874] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:46,874] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:48,887] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:48,887] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:50,893] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:50,894] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:52,902] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:52,902] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:54,910] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:54,910] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:56,918] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:56,918] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:38:58,926] INFO in appium_device_controller: Appium server started successfully
[2025-07-06 18:38:58,926] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-06 18:38:58,949] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-07-06 18:38:58,949] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-07-06 18:39:22,218] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET / HTTP/1.1" 200 -
[2025-07-06 18:39:22,234] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,237] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,237] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,245] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,245] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,246] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,250] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,250] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,251] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/css/test-case-modification.css HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,254] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,256] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,261] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,262] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,264] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,280] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,284] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,288] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /static/js/action-manager.js?v=1751794162 HTTP/1.1" 200 -
[2025-07-06 18:39:22,289] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,290] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,298] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,300] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,301] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,351] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,352] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,355] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,357] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,359] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,363] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,363] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,364] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /static/js/main.js?v=1751794162 HTTP/1.1" 200 -
[2025-07-06 18:39:22,368] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,369] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,374] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,375] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/import-export.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,376] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/js/test-case-modification.js HTTP/1.1[0m" 304 -
[2025-07-06 18:39:22,386] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-06 18:39:22,388] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/environments HTTP/1.1" 200 -
[2025-07-06 18:39:22,389] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-06 18:39:22,390] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:22,396] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-06 18:39:22,398] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-06 18:39:22,402] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:22,405] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-07-06 18:39:22,412] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/settings HTTP/1.1" 200 -
[2025-07-06 18:39:22,419] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-06 18:39:22,420] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-06 18:39:22,424] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/environments/current HTTP/1.1" 200 -
[2025-07-06 18:39:22,429] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-06 18:39:22,435] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-06 18:39:22,442] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-06 18:39:22,450] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-06 18:39:22,460] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-07-06 18:39:22,466] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/test_cases/action_types HTTP/1.1" 200 -
[2025-07-06 18:39:22,498] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-06 18:39:22,508] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-06 18:39:22,512] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/test_cases/locator_types HTTP/1.1" 200 -
[2025-07-06 18:39:22,520] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-07-06 18:39:22,531] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-06 18:39:22,751] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:22] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-07-06 18:39:24,151] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-06 18:39:24,157] INFO in appium_device_controller: Appium server is running and ready
[2025-07-06 18:39:24,158] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-06 18:39:24,159] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:24] "GET /api/devices HTTP/1.1" 200 -
[2025-07-06 18:39:26,261] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-06 18:39:26,265] INFO in appium_device_controller: Appium server is running and ready
[2025-07-06 18:39:26,265] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-06 18:39:26,265] INFO in appium_device_controller: Connecting to device: ********-00186C801E13C01E with options: None, platform hint: iOS
[2025-07-06 18:39:26,265] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-06 18:39:26,265] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-07-06 18:39:26,265] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-07-06 18:39:26,266] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '********-00186C801E13C01E', 'udid': '********-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-07-06 18:39:26,266] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '********-00186C801E13C01E', 'appium:udid': '********-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-07-06 18:39:26,266] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-07-06 18:39:26,284] INFO in appium_device_controller: Device ********-00186C801E13C01E is listed and trusted
[2025-07-06 18:39:26,285] INFO in appium_device_controller: Found port 8100 for device ********-00186C801E13C01E in wda_ports.txt
[2025-07-06 18:39:26,285] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device ********-00186C801E13C01E
[2025-07-06 18:39:27,387] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:27,388] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:27,390] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:27,391] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:32,385] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:32,386] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:32,389] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:32,390] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:36,289] INFO in appium_device_controller: WebDriverAgent not detected at http://localhost:8100, will try to start port forwarding: HTTPConnectionPool(host='localhost', port=8100): Read timed out. (read timeout=10)
[2025-07-06 18:39:36,290] INFO in appium_device_controller: Port 8100 is already in use, checking if WebDriverAgent is responding...
[2025-07-06 18:39:37,385] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:37,386] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:37,389] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:37,390] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:41,295] WARNING in appium_device_controller: Port 8100 is in use but WebDriverAgent is not responding: HTTPConnectionPool(host='localhost', port=8100): Read timed out. (read timeout=5). Will restart.
[2025-07-06 18:39:41,456] INFO in appium_device_controller: Killed process 95246 using port 8100
[2025-07-06 18:39:42,387] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:42,389] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:42,391] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:42,392] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:42,581] INFO in appium_device_controller: Using tidevice for port forwarding: 8100 -> 8100
[2025-07-06 18:39:44,592] INFO in appium_device_controller: tidevice port forwarding started successfully
[2025-07-06 18:39:44,593] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 1/5)...
[2025-07-06 18:39:44,602] INFO in appium_device_controller: WebDriverAgent is running at http://localhost:8100
[2025-07-06 18:39:44,602] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.0', 'time': 'Jun  8 2025 18:35:21', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': '87324228-B17F-4CCB-9117-DEA1B7973E18'}
[2025-07-06 18:39:44,608] INFO in appium_device_controller: Appium server is already running
[2025-07-06 18:39:44,608] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-07-06 18:39:44,608] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '********-00186C801E13C01E', 'appium:udid': '********-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8100'}
[2025-07-06 18:39:44,613] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:39:44,613] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-07-06 18:39:45,261] INFO in appium_device_controller: Wrapping driver with Healenium self-healing capabilities
[2025-07-06 18:39:45,264] INFO in appium_device_controller: Driver successfully wrapped with Healenium
[2025-07-06 18:39:45,264] INFO in appium_device_controller: Successfully connected to iOS device
[2025-07-06 18:39:45,264] INFO in appium_device_controller: Connected with session ID: b9917133-9663-4276-9462-3807c40f3d3c
[2025-07-06 18:39:45,264] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-07-06 18:39:45,264] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-07-06 18:39:45,265] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:39:46,045] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:39:46,045] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-07-06 18:39:46,048] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-07-06 18:39:46,049] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-07-06 18:39:46,049] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-07-06 18:39:46,049] INFO in appium_device_controller: iOS version: 18.0
[2025-07-06 18:39:46,049] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-07-06 18:39:46,049] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-06 18:39:46,049] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-06 18:39:46,050] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-06 18:39:46,050] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-06 18:39:46,051] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-06 18:39:46,051] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-06 18:39:46,052] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-06 18:39:46,052] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-06 18:39:46,052] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-06 18:39:46,053] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-06 18:39:46,053] INFO in action_factory: Registered action handler for 'wait'
[2025-07-06 18:39:46,054] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-06 18:39:46,054] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-06 18:39:46,054] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-06 18:39:46,055] INFO in action_factory: Registered action handler for 'text'
[2025-07-06 18:39:46,055] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-07-06 18:39:46,056] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-06 18:39:46,057] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-06 18:39:46,061] ERROR in action_factory: Error loading action handler from input_text_action: invalid syntax (input_text_action.py, line 226)
[2025-07-06 18:39:46,063] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-07-06 18:39:46,064] INFO in global_values_db: Global values database initialized successfully
[2025-07-06 18:39:46,064] INFO in global_values_db: Using global values from config.py
[2025-07-06 18:39:46,064] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-06 18:39:46,066] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-06 18:39:46,067] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-06 18:39:46,067] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-06 18:39:46,067] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-07-06 18:39:46,068] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-06 18:39:46,069] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-06 18:39:46,069] INFO in action_factory: Registered action handler for 'tap'
[2025-07-06 18:39:46,070] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-07-06 18:39:46,070] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-06 18:39:46,070] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-06 18:39:46,071] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-06 18:39:46,071] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-06 18:39:46,071] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-06 18:39:46,072] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-06 18:39:46,072] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-06 18:39:46,072] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-06 18:39:46,073] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-06 18:39:46,073] INFO in action_factory: Registered action handler for 'info'
[2025-07-06 18:39:46,073] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-06 18:39:46,074] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-06 18:39:46,074] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-06 18:39:46,075] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-06 18:39:46,075] INFO in action_factory: Registered action handler for 'exists'
[2025-07-06 18:39:46,075] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-06 18:39:46,076] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-06 18:39:46,076] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-06 18:39:46,076] INFO in action_factory: Registered action handler for 'test'
[2025-07-06 18:39:46,077] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-06 18:39:46,077] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-06 18:39:46,077] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-06 18:39:46,077] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-06 18:39:46,077] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-06 18:39:46,077] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-06 18:39:46,077] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-06 18:39:46,077] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'text': TextAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-06 18:39:46,078] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-06 18:39:46,079] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-06 18:39:46,079] INFO in action_factory: Handler for 'test': TestAction
[2025-07-06 18:39:46,079] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-06 18:39:46,079] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-06 18:39:46,079] INFO in appium_device_controller: Initializing Airtest connection for device: ********-00186C801E13C01E...
[2025-07-06 18:39:46,079] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-06 18:39:46,084] INFO in ios_device: Initialized MinimalIOSDevice for ********-00186C801E13C01E with WDA at http://localhost:8100
[2025-07-06 18:39:46,088] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-06 18:39:46,088] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:39:46,088] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:39:46,088] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:39:46,607] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:39:46,607] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:39:47,268] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:47] "POST /api/device/connect HTTP/1.1" 200 -
[2025-07-06 18:39:47,386] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:47,387] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:47,389] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:47,390] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:48,274] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:39:48,274] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:39:48,826] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:39:48,826] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:39:48,827] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:48] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1751791162384_ksaotrw5q_1751791162384_ksaotrw5q&t=1751791188272 HTTP/1.1" 200 -
[2025-07-06 18:39:52,386] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:52,387] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:52,389] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:52,390] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:57,386] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:57,387] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:39:57,389] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:39:57,390] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:39:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:40:02,387] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:40:02,388] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:40:02,391] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-06 18:40:02,392] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-06 18:40:02,467] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:02] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-06 18:40:02,495] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:02] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-06 18:40:04,270] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:04] "GET /api/test_cases/load/WishList_Copy_20250514185153_20250514185153.json HTTP/1.1" 200 -
[2025-07-06 18:40:04,281] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:04] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-07-06 18:40:05,637] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:05] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-07-06 18:40:05,651] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:05] "POST /api/database/clear_screenshots HTTP/1.1" 200 -
[2025-07-06 18:40:05,658] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:05,659] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:05,670] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:05] "GET /api/get_execution_context HTTP/1.1" 200 -
[2025-07-06 18:40:05,683] INFO in player: Executing action: {'action_id': 'HotUJOd6oB', 'executionTime': '3375ms', 'package_id': 'au.com.kmart', 'timestamp': 1746598746211, 'type': 'restartApp'}
[2025-07-06 18:40:05,683] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-07-06 18:40:05,683] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-07-06 18:40:05,683] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-07-06 18:40:05,683] INFO in player: DEBUG: Using action_id from action: HotUJOd6oB
[2025-07-06 18:40:05,683] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-07-06 18:40:05,683] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-07-06 18:40:05,683] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-07-06 18:40:05,683] INFO in player: ========== ACTION TYPE: restartApp ==========
[2025-07-06 18:40:05,684] INFO in player: ========== ACTION ID: HotUJOd6oB ==========
[2025-07-06 18:40:05,684] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-07-06 18:40:05,684] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-07-06 18:40:05,733] INFO in player: Tracked execution in database: test_idx=0, step_idx=0, action_type=restartApp, action_id=HotUJOd6oB
[2025-07-06 18:40:05,733] INFO in player: Skipping device connection verification for better performance
[2025-07-06 18:40:05,733] INFO in player: Restarting app: au.com.kmart
[2025-07-06 18:40:05,736] WARNING in player: Airtest restart failed: 'No devices added.', falling back to ADB
[2025-07-06 18:40:05,736] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-07-06 18:40:05,736] INFO in appium_device_controller: Using XCUITest to terminate iOS app: au.com.kmart
[2025-07-06 18:40:05,911] ERROR in appium_device_controller: Error terminating iOS app: Message: Session does not exist
Stacktrace:
NoSuchDriverError: Session does not exist
    at errorFromW3CJsonCode (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1110:25)
    at ProxyRequestError.getActualError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:979:14)
    at JWProxy.command (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:357:19)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:100:35)
    at XCUITestDriver.mobileTerminateApp (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/app-management.js:134:7)
    at XCUITestDriver.executeMethod (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/basedriver/commands/execute.ts:61:12)
    at XCUITestDriver.execute (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/execute.js:117:14)
[2025-07-06 18:40:05,911] ERROR in appium_device_controller: Error in terminate_app: Failed to terminate app: Message: Session does not exist
Stacktrace:
NoSuchDriverError: Session does not exist
    at errorFromW3CJsonCode (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1110:25)
    at ProxyRequestError.getActualError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:979:14)
    at JWProxy.command (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:357:19)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:100:35)
    at XCUITestDriver.mobileTerminateApp (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/app-management.js:134:7)
    at XCUITestDriver.executeMethod (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/basedriver/commands/execute.ts:61:12)
    at XCUITestDriver.execute (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/execute.js:117:14)
[2025-07-06 18:40:05,919] ERROR in appium_device_controller: Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 3712, in _execute_restart_app
    clear_app(package)
    ~~~~~~~~~^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/utils/logwraper.py", line 134, in wrapper
    res = f(*args, **kwargs)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/api.py", line 217, in clear_app
    G.DEVICE.clear_app(package)
    ^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/helper.py", line 20, in DEVICE
    raise NoDeviceError("No devices added.")
airtest.core.error.NoDeviceError: 'No devices added.'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/appium_device_controller.py", line 3860, in terminate_app
    self.driver.execute_script('mobile: terminateApp', {'bundleId': app_id})
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/healenium_wrapper.py", line 192, in wrapper
    return self._execute_with_fallback(name, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/healenium_wrapper.py", line 113, in _execute_with_fallback
    raise e
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/healenium_wrapper.py", line 92, in _execute_with_fallback
    result = method(*args, **kwargs)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py", line 528, in execute_script
    return self.execute(command, {"script": script, "args": converted_args})["value"]
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py", line 429, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/appium/webdriver/errorhandler.py", line 125, in check_response
    raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
selenium.common.exceptions.InvalidSessionIdException: Message: Session does not exist
Stacktrace:
NoSuchDriverError: Session does not exist
    at errorFromW3CJsonCode (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1110:25)
    at ProxyRequestError.getActualError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:979:14)
    at JWProxy.command (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:357:19)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:100:35)
    at XCUITestDriver.mobileTerminateApp (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/app-management.js:134:7)
    at XCUITestDriver.executeMethod (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/basedriver/commands/execute.ts:61:12)
    at XCUITestDriver.execute (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/execute.js:117:14)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/appium_device_controller.py", line 3867, in terminate_app
    raise Exception(f"Failed to terminate app: {e}")
Exception: Failed to terminate app: Message: Session does not exist
Stacktrace:
NoSuchDriverError: Session does not exist
    at errorFromW3CJsonCode (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1110:25)
    at ProxyRequestError.getActualError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:979:14)
    at JWProxy.command (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:357:19)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:100:35)
    at XCUITestDriver.mobileTerminateApp (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/app-management.js:134:7)
    at XCUITestDriver.executeMethod (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/basedriver/commands/execute.ts:61:12)
    at XCUITestDriver.execute (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/execute.js:117:14)

[2025-07-06 18:40:05,919] INFO in player: Skipping delay after action execution for better performance
[2025-07-06 18:40:05,919] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-07-06 18:40:05,919] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-07-06 18:40:05,919] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-07-06 18:40:05,919] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-07-06 18:40:05,919] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-07-06 18:40:05,920] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-07-06 18:40:05,920] INFO in player: ========== ACTION TYPE: restartApp ==========
[2025-07-06 18:40:05,920] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-07-06 18:40:05,920] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-07-06 18:40:05,926] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=0, action_type=restartApp, status=failed
[2025-07-06 18:40:05,927] INFO in player: Action failed with error: Failed to terminate app: Failed to terminate app: Message: Session does not exist
Stacktrace:
NoSuchDriverError: Session does not exist
    at errorFromW3CJsonCode (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1110:25)
    at ProxyRequestError.getActualError (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:979:14)
    at JWProxy.command (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:357:19)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:100:35)
    at XCUITestDriver.mobileTerminateApp (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/app-management.js:134:7)
    at XCUITestDriver.executeMethod (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/basedriver/commands/execute.ts:61:12)
    at XCUITestDriver.execute (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/execute.js:117:14)
[2025-07-06 18:40:06,225] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:06,225] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:06,226] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:06] "GET /screenshot?deviceId=********-00186C801E13C01E&clientSessionId=client_1751791162384_ksaotrw5q_1751791162384_ksaotrw5q&t=1751791188272 HTTP/1.1" 200 -
[2025-07-06 18:40:06,927] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:06,927] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:06,927] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:07,478] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-06 18:40:07,478] INFO in appium_device_controller: suite_id: 
[2025-07-06 18:40:07,478] INFO in appium_device_controller: test_idx: 0
[2025-07-06 18:40:07,478] INFO in appium_device_controller: step_idx: 0
[2025-07-06 18:40:07,478] INFO in appium_device_controller: filename: placeholder.png
[2025-07-06 18:40:07,478] INFO in appium_device_controller: action_id: placeholder
[2025-07-06 18:40:07,479] INFO in appium_device_controller: Saved screenshot info to database for step 0_0
[2025-07-06 18:40:07,479] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:07,487] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:07] "POST /api/action/execute HTTP/1.1" 200 -
[2025-07-06 18:40:07,495] INFO in _internal: 127.0.0.1 - - [06/Jul/2025 18:40:07] "GET /api/get_execution_context HTTP/1.1" 200 -
[2025-07-06 18:40:07,504] INFO in player: Executing action: {'action_id': 'rkL0oz4kiL', 'executionTime': '4853ms', 'interval': 0.5, 'locator_type': 'accessibility_id', 'locator_value': 'txtHomeAccountCtaSignIn', 'method': 'locator', 'timeout': 10, 'timestamp': *************, 'type': 'tap'}
[2025-07-06 18:40:07,504] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-07-06 18:40:07,504] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-07-06 18:40:07,504] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-07-06 18:40:07,504] INFO in player: DEBUG: Using action_id from action: rkL0oz4kiL
[2025-07-06 18:40:07,504] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-07-06 18:40:07,504] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-07-06 18:40:07,504] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-07-06 18:40:07,504] INFO in player: ========== ACTION TYPE: tap ==========
[2025-07-06 18:40:07,504] INFO in player: ========== ACTION ID: rkL0oz4kiL ==========
[2025-07-06 18:40:07,504] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-07-06 18:40:07,504] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-07-06 18:40:07,511] INFO in player: Tracked execution in database: test_idx=0, step_idx=1, action_type=tap, action_id=rkL0oz4kiL
[2025-07-06 18:40:07,511] INFO in player: Skipping device connection verification for better performance
[2025-07-06 18:40:07,511] INFO in player: Tapping on element with accessibility_id: txtHomeAccountCtaSignIn, timeout=10s
[2025-07-06 18:40:07,511] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-06 18:40:07,511] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-06 18:40:07,511] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-06 18:40:07,512] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'wait'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-06 18:40:07,512] INFO in action_factory: Registered action handler for 'text'
[2025-07-06 18:40:07,514] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-07-06 18:40:07,514] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-06 18:40:07,514] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-06 18:40:07,518] ERROR in action_factory: Error loading action handler from input_text_action: invalid syntax (input_text_action.py, line 226)
[2025-07-06 18:40:07,518] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-06 18:40:07,518] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'tap'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-07-06 18:40:07,519] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-06 18:40:07,519] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-06 18:40:07,519] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-06 18:40:07,519] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'info'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-06 18:40:07,519] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-06 18:40:07,520] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action handler for 'exists'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action handler for 'test'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-06 18:40:07,520] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-06 18:40:07,520] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-06 18:40:07,520] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-06 18:40:07,520] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-06 18:40:07,520] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-06 18:40:07,520] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-06 18:40:07,520] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-06 18:40:07,520] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-06 18:40:07,520] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-06 18:40:07,520] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'text': TextAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-06 18:40:07,521] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-06 18:40:07,522] INFO in action_factory: Handler for 'test': TestAction
[2025-07-06 18:40:07,522] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-06 18:40:07,522] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-06 18:40:07,522] INFO in action_factory: Requested action type: 'tap', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifElseSteps', 'info', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-07-06 18:40:07,522] INFO in action_factory: Action parameters before env resolution: {'method': 'locator', 'locator_type': 'accessibility_id', 'locator_value': 'txtHomeAccountCtaSignIn', 'timeout': 10, 'interval': 0.5}
[2025-07-06 18:40:07,531] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-07-06 18:40:07,532] INFO in global_values_db: Global values database initialized successfully
[2025-07-06 18:40:07,533] INFO in global_values_db: Using global values from config.py
[2025-07-06 18:40:07,533] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-06 18:40:07,535] INFO in healenium_config: Loaded Healenium configuration: enabled=True
[2025-07-06 18:40:07,536] WARNING in appium_device_controller: TouchAction not available in this Appium Python Client version - using W3C Actions fallback
[2025-07-06 18:40:07,579] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-06 18:40:07,601] INFO in app: Using directories from config.py:
[2025-07-06 18:40:07,602] INFO in app:   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
[2025-07-06 18:40:07,602] INFO in app:   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
[2025-07-06 18:40:07,602] INFO in app:   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-06 18:40:07,603] INFO in appium_device_controller: Tapping on element with accessibility_id='txtHomeAccountCtaSignIn' (timeout=10s, interval=0.5s)
[2025-07-06 18:40:07,603] INFO in appium_device_controller: Waiting for element to be clickable: accessibility_id='txtHomeAccountCtaSignIn'
[2025-07-06 18:40:15,266] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-06 18:40:15,271] INFO in appium_device_controller: Appium server is running and ready
[2025-07-06 18:40:15,271] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-06 18:40:15,271] INFO in appium_device_controller: Connecting to device: ********-00186C801E13C01E with options: None, platform hint: iOS
[2025-07-06 18:40:15,271] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-06 18:40:15,272] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-07-06 18:40:15,272] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-07-06 18:40:15,272] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '********-00186C801E13C01E', 'udid': '********-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-07-06 18:40:15,272] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '********-00186C801E13C01E', 'appium:udid': '********-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-07-06 18:40:15,272] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-07-06 18:40:15,291] INFO in appium_device_controller: Device ********-00186C801E13C01E is listed and trusted
[2025-07-06 18:40:15,293] INFO in appium_device_controller: Found port 8100 for device ********-00186C801E13C01E in wda_ports.txt
[2025-07-06 18:40:15,293] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device ********-00186C801E13C01E
[2025-07-06 18:40:15,303] INFO in appium_device_controller: WebDriverAgent is already running at http://localhost:8100
[2025-07-06 18:40:15,303] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.0', 'time': 'Jun  8 2025 18:35:21', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': None}
[2025-07-06 18:40:15,307] INFO in appium_device_controller: Appium server is already running
[2025-07-06 18:40:15,307] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-07-06 18:40:15,307] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '********-00186C801E13C01E', 'appium:udid': '********-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8100'}
[2025-07-06 18:40:15,312] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '753d67d40edcfb21f5bae301b7244d1b71d806fa', 'built': '2025-07-06 17:12:34 +1000'}}}
[2025-07-06 18:40:15,312] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-07-06 18:40:15,378] ERROR in appium_device_controller: Error in tap_element: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
[2025-07-06 18:40:15,381] ERROR in appium_device_controller: Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/appium_device_controller.py", line 3131, in tap_element
    element = WebDriverWait(self.driver, timeout, interval).until(
        EC.element_to_be_clickable((by_type, locator_value))
    )
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/support/wait.py", line 137, in until
    value = method(self._driver)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/support/expected_conditions.py", line 633, in _predicate
    target = driver.find_element(*target)  # grab element at locator
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/healenium_wrapper.py", line 118, in find_element
    return self._execute_with_fallback('find_element', by, value)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/healenium_wrapper.py", line 113, in _execute_with_fallback
    raise e
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/healenium_wrapper.py", line 92, in _execute_with_fallback
    result = method(*args, **kwargs)
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py", line 898, in find_element
    return self.execute(Command.FIND_ELEMENT, {"using": by, "value": value})["value"]
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py", line 429, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/appium/webdriver/errorhandler.py", line 125, in check_response
    raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
selenium.common.exceptions.InvalidSessionIdException: Message: A session is either terminated or not started
Stacktrace:
NoSuchDriverError: A session is either terminated or not started
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:311:15)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:514:15
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:284:15
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:365:14)
    at param (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:376:14)
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:421:3)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at logger (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/morgan/index.js:144:5)
    at Layer.handle [as handle_request] (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:328:13)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/express/lib/router/index.js:280:10)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/body-parser/lib/read.js:137:5
    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)
    at invokeCallback (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:238:16)
    at done (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:227:7)
    at IncomingMessage.onEnd (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/raw-body/index.js:287:7)
    at IncomingMessage.emit (node:events:507:28)
    at endReadableNT (node:internal/streams/readable:1696:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)

[2025-07-06 18:40:15,860] INFO in appium_device_controller: Wrapping driver with Healenium self-healing capabilities
[2025-07-06 18:40:15,864] INFO in appium_device_controller: Driver successfully wrapped with Healenium
[2025-07-06 18:40:15,864] INFO in appium_device_controller: Successfully connected to iOS device
[2025-07-06 18:40:15,864] INFO in appium_device_controller: Connected with session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:40:15,864] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-07-06 18:40:15,864] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-07-06 18:40:15,864] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:40:16,392] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:16,392] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:16,392] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:16,393] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:16,561] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:40:16,561] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-07-06 18:40:16,563] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-07-06 18:40:16,563] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-07-06 18:40:16,563] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-07-06 18:40:16,563] INFO in appium_device_controller: iOS version: 18.0
[2025-07-06 18:40:16,563] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-07-06 18:40:16,563] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-06 18:40:16,563] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-06 18:40:16,566] INFO in appium_device_controller: Initializing Airtest connection for device: ********-00186C801E13C01E...
[2025-07-06 18:40:16,567] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-06 18:40:16,669] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-06 18:40:16,669] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:16,669] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:16,669] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:16,669] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:17,067] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:17,068] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:40:17,068] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:17,163] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:17,164] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:40:17,164] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:18,855] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:18,855] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:19,399] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:19,399] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:19,399] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:22,326] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:22,326] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:22,350] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-07-06 18:40:22,350] INFO in appium_device_controller: Using XCUITest to terminate iOS app: au.com.kmart
[2025-07-06 18:40:23,905] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:23,905] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:23,905] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:24,561] INFO in appium_device_controller: Launching app: au.com.kmart
[2025-07-06 18:40:24,561] INFO in appium_device_controller: Using XCUITest to launch iOS app: au.com.kmart
[2025-07-06 18:40:27,025] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:27,025] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:27,025] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:27,025] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:28,036] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:28,037] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-07-06 18:40:28,037] INFO in appium_device_controller: suite_id: 
[2025-07-06 18:40:28,037] INFO in appium_device_controller: test_idx: 0
[2025-07-06 18:40:28,037] INFO in appium_device_controller: step_idx: 2
[2025-07-06 18:40:28,037] INFO in appium_device_controller: filename: placeholder.png
[2025-07-06 18:40:28,037] INFO in appium_device_controller: action_id: placeholder
[2025-07-06 18:40:28,038] INFO in appium_device_controller: Saved screenshot info to database for step 0_2
[2025-07-06 18:40:28,038] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:28,048] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:28,048] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:28,578] INFO in appium_device_controller: Tapping on element with accessibility_id='txtHomeAccountCtaSignIn' (timeout=10s, interval=0.5s)
[2025-07-06 18:40:28,579] INFO in appium_device_controller: Waiting for element to be clickable: accessibility_id='txtHomeAccountCtaSignIn'
[2025-07-06 18:40:29,113] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:29,113] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:29,113] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:32,214] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:40:34,807] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:34,807] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:34,807] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:34,807] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:35,759] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:35,760] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:40:35,760] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:35,769] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:35,769] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:36,734] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:36,734] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:36,734] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:38,443] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:38,443] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:38,443] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:38,444] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:39,041] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:39,041] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:40:39,041] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:39,050] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:39,050] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:39,650] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:39,650] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:39,650] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:42,599] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:42,600] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:42,600] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:42,600] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:43,263] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:43,264] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:40:43,264] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:43,276] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:43,276] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:43,893] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:43,894] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:43,894] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:46,869] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:46,869] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:46,869] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:46,869] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:47,467] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:47,468] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:40:47,468] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:47,477] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:47,477] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:47,854] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:40:48,004] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeTextField[@name="Email"]' (timeout=10s, interval=0.5s)
[2025-07-06 18:40:48,004] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeTextField[@name="Email"]'
[2025-07-06 18:40:48,109] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:48,109] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:48,109] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:48,689] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:40:50,444] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:40:52,247] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:52,247] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:52,247] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:52,247] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:52,329] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:40:53,224] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:40:53,564] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:53,564] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:40:53,565] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:53,573] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:53,573] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:54,103] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:54,103] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:54,103] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:40:58,280] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:40:58,281] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:40:58,281] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:40:58,281] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:58,876] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:58,877] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:40:58,877] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:40:58,886] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:40:58,886] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:40:59,415] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeSecureTextField[@name="Password"]' (timeout=10s, interval=0.5s)
[2025-07-06 18:40:59,415] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeSecureTextField[@name="Password"]'
[2025-07-06 18:40:59,497] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:40:59,497] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:40:59,497] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:01,221] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:41:03,003] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:03,003] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:03,003] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:03,003] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:03,586] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:03,586] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:03,586] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:03,596] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:03,596] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:04,206] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:04,207] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:04,207] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:08,043] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:08,043] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:08,043] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:08,043] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:08,609] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:08,610] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:08,610] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:08,619] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:08,619] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:09,120] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:09,120] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:09,171] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:09,171] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:09,171] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:09,171] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:09,622] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:09,622] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:09,622] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:10,212] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:10,213] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:10,213] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:10,234] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:10,234] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:10,234] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:14,439] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:14,440] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:14,440] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:14,440] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:14,968] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:14,969] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:14,969] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:14,981] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:14,981] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:15,488] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:15,488] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:15,488] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:17,857] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:41:18,861] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:41:19,070] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:19,071] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:19,071] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:19,071] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:19,603] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:19,604] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:19,604] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:19,614] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:19,614] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:20,530] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:20,530] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:20,530] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:22,329] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:41:23,119] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:23,120] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:23,120] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:23,120] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:23,246] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:41:24,095] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:24,096] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:24,096] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:24,107] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:24,107] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:25,097] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:25,097] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:25,097] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:27,683] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:27,683] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:27,684] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:27,684] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:28,608] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:28,609] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:28,609] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:28,621] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:28,621] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:29,166] INFO in appium_device_controller: Tapping on element with xpath='(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]' (timeout=10s, interval=0.5s)
[2025-07-06 18:41:29,166] INFO in appium_device_controller: Waiting for element to be clickable: xpath='(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]'
[2025-07-06 18:41:29,586] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:29,586] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:29,586] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:31,014] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:41:32,766] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:32,767] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:32,767] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:32,767] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:33,717] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:33,717] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:33,717] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:33,728] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:33,728] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:34,648] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:34,648] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:34,648] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:37,533] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:37,534] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:37,534] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:37,534] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:38,457] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:38,457] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:38,457] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:38,468] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:38,468] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:38,981] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:41:39,403] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:39,403] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:39,403] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:39,978] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:41:42,239] INFO in appium_device_controller: Swiped from (196,596) to (196,255)
[2025-07-06 18:41:46,017] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:46,017] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:46,017] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:46,018] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:46,924] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:46,925] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:46,925] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:46,935] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:46,935] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:47,469] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]' (timeout=10s, interval=0.5s)
[2025-07-06 18:41:47,469] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]'
[2025-07-06 18:41:47,855] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:41:47,884] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:47,884] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:47,884] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:49,915] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:41:50,416] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:41:52,194] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:52,194] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:52,195] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:52,195] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:52,328] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:41:53,391] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:41:54,021] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:54,022] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:54,022] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:54,033] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:54,033] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:54,569] INFO in appium_device_controller: Initializing Airtest connection for device: ********-00186C801E13C01E...
[2025-07-06 18:41:54,569] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-06 18:41:54,583] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-06 18:41:54,956] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:54,956] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:54,956] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:41:58,062] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:41:58,062] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:41:58,062] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:41:58,062] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:58,981] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:58,982] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:41:58,982] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:41:58,993] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:41:58,993] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:41:59,533] INFO in appium_device_controller: Tapping on element with xpath='(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]' (timeout=10s, interval=0.5s)
[2025-07-06 18:41:59,533] INFO in appium_device_controller: Waiting for element to be clickable: xpath='(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]'
[2025-07-06 18:41:59,924] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:41:59,924] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:41:59,924] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:01,346] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:42:03,093] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:03,093] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:03,094] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:03,094] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:04,112] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:04,113] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:04,113] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:04,123] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:04,123] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:05,154] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:05,154] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:05,154] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:07,877] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:07,877] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:07,877] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:07,877] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:08,934] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:08,935] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:08,935] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:08,949] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:08,949] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:09,464] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:42:10,002] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:10,002] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:10,002] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:10,478] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:42:12,678] INFO in appium_device_controller: Swiped from (196,596) to (196,255)
[2025-07-06 18:42:16,428] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:16,429] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:16,429] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:16,429] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:17,316] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:17,317] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:17,317] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:17,327] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:17,328] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:17,867] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:42:17,871] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]' (timeout=10s, interval=0.5s)
[2025-07-06 18:42:17,871] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]'
[2025-07-06 18:42:18,227] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:18,227] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:18,227] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:18,878] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:42:20,803] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:42:22,330] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:42:22,587] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:22,587] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:22,587] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:22,587] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:23,344] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:42:24,136] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:24,137] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:24,137] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:24,148] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:24,148] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:24,689] INFO in appium_device_controller: Initializing Airtest connection for device: ********-00186C801E13C01E...
[2025-07-06 18:42:24,690] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-06 18:42:24,705] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-06 18:42:25,057] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:25,057] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:25,057] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:28,177] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:28,177] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:28,178] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:28,178] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:29,157] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:29,157] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:29,157] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:29,169] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:29,169] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:30,092] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:30,092] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:30,092] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:32,751] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:32,751] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:32,751] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:32,752] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:33,705] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:33,707] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:33,707] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:33,721] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:33,721] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:34,317] INFO in appium_device_controller: Tapping on element with xpath='(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]' (timeout=10s, interval=0.5s)
[2025-07-06 18:42:34,317] INFO in appium_device_controller: Waiting for element to be clickable: xpath='(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]'
[2025-07-06 18:42:34,704] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:34,704] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:34,704] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:36,163] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:42:37,902] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:37,902] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:37,902] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:37,902] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:38,411] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:38,412] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:38,412] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:38,421] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:38,421] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:39,256] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:39,256] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:39,256] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:41,851] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:41,851] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:41,851] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:41,851] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:42,633] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:42,634] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:42,634] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:42,645] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:42,645] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:43,164] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:42:43,451] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:43,451] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:43,451] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:44,016] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:42:46,067] INFO in appium_device_controller: Swiped from (196,596) to (196,255)
[2025-07-06 18:42:47,857] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:42:49,129] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:42:50,367] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:50,367] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:50,367] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:50,368] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:51,092] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:51,093] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:51,093] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:51,104] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:51,104] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:51,645] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]' (timeout=10s, interval=0.5s)
[2025-07-06 18:42:51,645] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]'
[2025-07-06 18:42:51,863] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:51,863] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:51,863] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:52,330] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:42:53,722] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:42:54,157] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:42:55,880] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:42:55,880] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:42:55,880] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:42:55,880] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:56,614] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:56,615] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:42:56,615] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:42:56,626] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:42:56,627] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:42:57,175] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]' (timeout=10s, interval=0.5s)
[2025-07-06 18:42:57,175] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]'
[2025-07-06 18:42:57,367] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:42:57,367] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:42:57,367] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:42:58,903] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:43:00,626] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:00,626] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:00,626] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:00,626] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:01,401] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:01,403] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:01,403] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:01,414] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:01,414] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:02,135] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:02,135] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:02,135] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:04,481] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:04,481] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:04,481] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:04,482] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:05,183] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:05,184] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:05,184] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:05,195] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:05,195] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:05,741] INFO in appium_device_controller: Tapping on element with xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]' (timeout=10s, interval=0.5s)
[2025-07-06 18:43:05,741] INFO in appium_device_controller: Waiting for element to be clickable: xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]'
[2025-07-06 18:43:05,926] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:05,926] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:05,926] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:07,315] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:43:08,994] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:08,994] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:08,994] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:08,994] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:09,768] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:09,770] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:09,770] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:09,780] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:09,781] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:10,600] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:10,600] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:10,600] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:16,306] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:16,306] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:16,306] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:16,306] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:17,050] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:17,051] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:17,051] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:17,061] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:17,061] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:17,610] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:17,610] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:17,610] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:17,610] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:17,834] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:17,834] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:17,834] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:17,856] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:43:18,357] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:18,358] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:18,358] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:18,568] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:43:21,806] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:21,806] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:21,806] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:21,807] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:22,331] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:43:22,520] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:22,521] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:22,521] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:22,530] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:22,531] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:23,076] INFO in appium_device_controller: Tapping on element with xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]' (timeout=10s, interval=0.5s)
[2025-07-06 18:43:23,076] INFO in appium_device_controller: Waiting for element to be clickable: xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]'
[2025-07-06 18:43:23,078] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:43:24,795] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:43:24,893] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:24,893] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:24,893] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:26,496] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:26,496] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:26,496] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:26,496] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:27,285] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:27,286] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:27,286] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:27,297] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:27,297] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:28,074] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:28,074] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:28,074] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:33,825] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:33,825] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:33,825] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:33,825] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:34,590] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:34,590] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:34,590] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:34,601] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:34,601] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:35,142] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:35,143] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:35,143] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:35,143] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:35,403] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:35,403] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:35,403] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:35,906] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:35,907] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:35,908] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:39,160] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:39,161] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:39,161] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:39,161] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:39,866] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:39,867] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:39,867] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:39,879] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:39,879] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:40,424] INFO in appium_device_controller: Tapping on element with xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]' (timeout=10s, interval=0.5s)
[2025-07-06 18:43:40,424] INFO in appium_device_controller: Waiting for element to be clickable: xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]'
[2025-07-06 18:43:40,586] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:40,586] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:40,586] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:41,983] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:43:43,654] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:43,654] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:43,655] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:43,655] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:44,154] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:44,155] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:44,155] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:44,166] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:44,166] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:45,147] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:45,147] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:45,147] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:47,859] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:43:47,903] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:47,903] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:47,903] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:47,903] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:48,866] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:43:49,746] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:49,747] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:49,747] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:49,758] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:49,758] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:50,307] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]' (timeout=10s, interval=0.5s)
[2025-07-06 18:43:50,307] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]'
[2025-07-06 18:43:50,808] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:50,808] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:50,808] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:52,313] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:43:52,331] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:43:54,086] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:43:54,086] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:43:54,086] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:43:54,086] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:54,127] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:43:54,548] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:54,548] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:43:54,548] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:43:54,560] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:43:54,560] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:43:55,027] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:43:55,027] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:43:55,027] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:43:55,112] INFO in appium_device_controller: Finding element with xpath: //XCUIElementTypeButton[@name="Checkout"], timeout=10s
[2025-07-06 18:44:05,273] INFO in appium_device_controller: Element with xpath: //XCUIElementTypeButton[@name="Checkout"] not found within timeout of 10 seconds
[2025-07-06 18:44:06,284] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:06,284] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:06,284] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:06,284] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:06,874] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:06,876] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:06,876] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:06,886] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:06,886] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:07,399] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:44:07,498] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:07,498] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:07,498] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:08,209] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:44:10,248] INFO in appium_device_controller: Swiped from (196,596) to (196,255)
[2025-07-06 18:44:13,220] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:13,220] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:13,220] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:13,220] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:13,850] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:13,851] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:13,851] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:13,862] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:13,862] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:14,487] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:14,487] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:14,487] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:17,243] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:17,243] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:17,243] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:17,243] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:17,858] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:44:17,860] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:17,860] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:17,860] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:17,872] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:17,872] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:18,426] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[@name="Move to wishlist"]' (timeout=10s, interval=0.5s)
[2025-07-06 18:44:18,426] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[@name="Move to wishlist"]'
[2025-07-06 18:44:18,680] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:44:20,400] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:20,401] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:20,401] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:20,410] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:44:22,113] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:22,113] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:22,113] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:22,114] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:22,330] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:44:22,613] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:22,613] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:22,614] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:22,622] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:22,622] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:23,007] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:44:23,174] INFO in appium_device_controller: Initializing Airtest connection for device: ********-00186C801E13C01E...
[2025-07-06 18:44:23,174] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-06 18:44:23,183] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-06 18:44:23,458] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:23,458] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:23,458] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:26,320] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:26,320] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:26,320] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:26,320] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:27,243] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:27,245] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:27,245] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:27,255] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:27,256] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:27,800] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]' (timeout=10s, interval=0.5s)
[2025-07-06 18:44:27,800] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]'
[2025-07-06 18:44:28,213] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:28,213] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:28,213] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:29,814] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:44:31,604] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:31,604] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:31,604] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:31,604] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:32,325] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:32,326] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:32,326] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:32,336] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:32,336] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:32,879] INFO in appium_device_controller: Finding element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2], timeout=10s
[2025-07-06 18:44:33,056] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:33,056] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:33,056] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:34,453] INFO in appium_device_controller: Tapping on element with xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]' (timeout=10s, interval=0.5s)
[2025-07-06 18:44:34,453] INFO in appium_device_controller: Waiting for element to be clickable: xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]'
[2025-07-06 18:44:35,929] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:44:37,613] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:37,613] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:37,613] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:37,613] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:38,365] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:38,366] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:38,366] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:38,376] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:38,376] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:38,925] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:38,925] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:38,925] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:38,925] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:39,148] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:39,148] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:39,148] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:39,668] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:39,669] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:39,669] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:42,913] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:42,913] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:42,914] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:42,914] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:43,603] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:43,604] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:43,604] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:43,614] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:43,614] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:44,155] INFO in appium_device_controller: Finding element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2], timeout=10s
[2025-07-06 18:44:44,323] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:44,323] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:44,323] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:45,736] INFO in appium_device_controller: Tapping on element with xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]' (timeout=10s, interval=0.5s)
[2025-07-06 18:44:45,736] INFO in appium_device_controller: Waiting for element to be clickable: xpath='(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]'
[2025-07-06 18:44:47,223] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:44:47,860] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:44:48,606] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:44:48,916] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:48,916] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:48,916] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:48,916] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:49,664] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:49,665] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:49,665] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:49,675] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:49,675] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:50,224] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:50,224] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:50,224] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:50,224] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:50,430] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:50,430] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:50,430] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:50,947] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:50,947] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:50,947] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:52,331] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:44:53,867] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:44:54,164] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:54,164] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:54,164] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:54,164] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:54,802] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:54,804] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:54,804] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:54,814] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:54,814] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:55,352] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]' (timeout=10s, interval=0.5s)
[2025-07-06 18:44:55,352] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]'
[2025-07-06 18:44:55,462] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:55,463] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:55,463] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:44:56,902] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:44:58,596] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:44:58,596] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:44:58,597] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:44:58,597] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:59,144] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:59,144] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:44:59,144] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:44:59,155] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:44:59,155] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:44:59,677] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:44:59,725] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:44:59,725] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:44:59,725] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:00,422] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:45:02,412] INFO in appium_device_controller: Swiped from (196,596) to (196,255)
[2025-07-06 18:45:03,419] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:03,419] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:03,420] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:03,420] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:03,944] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:03,945] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:03,945] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:03,955] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:03,956] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:04,508] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[@name="txtSign out"]' (timeout=10s, interval=0.5s)
[2025-07-06 18:45:04,508] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[@name="txtSign out"]'
[2025-07-06 18:45:04,509] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:04,509] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:04,509] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:06,107] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:45:07,799] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:07,799] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:07,799] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:07,799] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:08,775] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:08,776] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:08,776] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:08,787] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:08,787] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:09,310] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:45:09,792] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:09,792] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:09,792] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:10,097] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:45:11,748] INFO in appium_device_controller: Swiped from (196,596) to (196,85)
[2025-07-06 18:45:12,759] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:12,759] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:12,759] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:12,759] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:14,155] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:14,156] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:14,156] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:14,169] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:14,169] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:14,712] INFO in appium_device_controller: Terminating app: nz.com.kmart
[2025-07-06 18:45:14,712] INFO in appium_device_controller: Using XCUITest to terminate iOS app: nz.com.kmart
[2025-07-06 18:45:15,674] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:15,674] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:15,675] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:16,750] INFO in appium_device_controller: Launching app: nz.com.kmart
[2025-07-06 18:45:16,750] INFO in appium_device_controller: Using XCUITest to launch iOS app: nz.com.kmart
[2025-07-06 18:45:17,860] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:45:18,107] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:45:19,059] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:19,060] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:19,060] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:19,060] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:20,102] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:20,102] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:20,102] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:20,113] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:20,113] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:21,084] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:21,084] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:21,084] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:22,335] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:45:23,093] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:45:26,630] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:26,630] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:26,630] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:26,631] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:27,630] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:27,630] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:27,630] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:27,641] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:27,641] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:28,172] INFO in appium_device_controller: Tapping on element with xpath='//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]' (timeout=10s, interval=0.5s)
[2025-07-06 18:45:28,172] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]'
[2025-07-06 18:45:28,644] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:28,645] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:28,645] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:29,601] INFO in appium_device_controller: Element found, tapping on it
[2025-07-06 18:45:31,229] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:31,230] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:31,230] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:31,230] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:31,808] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:31,808] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:31,809] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:31,819] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:31,819] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:32,325] INFO in appium_device_controller: Getting device dimensions
[2025-07-06 18:45:32,417] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:32,417] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:32,417] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:33,000] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-06 18:45:34,546] INFO in appium_device_controller: Swiped from (196,596) to (196,255)
[2025-07-06 18:45:35,555] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:35,555] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:35,555] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:35,555] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:36,106] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:36,107] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:36,107] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:36,116] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:36,116] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:36,643] INFO in appium_device_controller: Finding element with xpath: //XCUIElementTypeButton[@name="txtSign out"], timeout=10s
[2025-07-06 18:45:36,665] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:36,665] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:36,665] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:46,884] INFO in appium_device_controller: Element with xpath: //XCUIElementTypeButton[@name="txtSign out"] not found within timeout of 10 seconds
[2025-07-06 18:45:47,861] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:45:47,890] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:47,891] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:47,891] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:47,891] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:48,535] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:45:49,052] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:49,052] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:49,052] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:49,061] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:49,061] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:49,571] INFO in appium_device_controller: Terminating app: au.com.kmart
[2025-07-06 18:45:49,572] INFO in appium_device_controller: Using XCUITest to terminate iOS app: au.com.kmart
[2025-07-06 18:45:49,619] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:49,619] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:49,619] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:51,714] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots
[2025-07-06 18:45:51,714] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-06 18:45:51,714] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/placeholder.png (save_debug=False)
[2025-07-06 18:45:51,715] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:52,256] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:52,256] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-06 18:45:52,256] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-06 18:45:52,266] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:52,266] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:52,334] DEBUG in appium_device_controller: Session ID: eb85c9ff-7761-404c-86bf-89fda35d6bda
[2025-07-06 18:45:52,769] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_********_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-06 18:45:52,769] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-06 18:45:53,104] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-06 18:45:53,440] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:53,440] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:53,440] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
[2025-07-06 18:45:53,547] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_6e00bb00-a1e5-47bc-a7f7-42aa8cb30420/screenshots/latest.png
[2025-07-06 18:45:53,547] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-06 18:45:53,547] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_********_00186C801E13C01E_latest.png
